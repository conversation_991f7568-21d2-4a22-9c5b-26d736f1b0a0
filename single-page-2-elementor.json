{"title": "Single Page 2", "type": "page", "version": "0.4", "page_settings": {"background_background": "classic", "background_color": "#ffffff", "content_wrapper_html_tag": "main"}, "content": [{"id": "header001", "elType": "container", "isInner": false, "settings": {"background_background": "classic", "background_color": "#ffffff", "padding": {"unit": "px", "top": "20", "right": "20", "bottom": "20", "left": "20", "isLinked": false}, "border_border": "solid", "border_width": {"unit": "px", "top": "0", "right": "0", "bottom": "1", "left": "0", "isLinked": false}, "border_color": "#e0e0e0"}, "elements": [{"id": "logo001", "elType": "widget", "widgetType": "heading", "isInner": false, "settings": {"title": "Your Logo", "size": "h2", "align": "left", "color": "#333333", "typography_typography": "custom", "typography_font_family": "<PERSON>l, sans-serif", "typography_font_size": {"unit": "px", "size": 28, "sizes": []}, "typography_font_weight": "bold"}, "elements": []}]}, {"id": "hero001", "elType": "container", "isInner": false, "settings": {"background_background": "gradient", "background_color": "#667eea", "background_color_b": "#764ba2", "background_gradient_angle": {"unit": "deg", "size": 45, "sizes": []}, "padding": {"unit": "px", "top": "100", "right": "20", "bottom": "100", "left": "20", "isLinked": false}, "min_height": {"unit": "vh", "size": 70, "sizes": []}, "content_position": "middle"}, "elements": [{"id": "hero_title", "elType": "widget", "widgetType": "heading", "isInner": false, "settings": {"title": "Welcome to Our Amazing Service", "size": "h1", "align": "center", "color": "#ffffff", "typography_typography": "custom", "typography_font_family": "<PERSON>l, sans-serif", "typography_font_size": {"unit": "px", "size": 48, "sizes": []}, "typography_font_weight": "bold", "margin": {"unit": "px", "top": "0", "right": "0", "bottom": "20", "left": "0", "isLinked": false}}, "elements": []}, {"id": "hero_subtitle", "elType": "widget", "widgetType": "text-editor", "isInner": false, "settings": {"editor": "<p>Discover the power of innovation with our cutting-edge solutions designed to transform your business and exceed your expectations.</p>", "align": "center", "text_color": "#ffffff", "typography_typography": "custom", "typography_font_family": "<PERSON>l, sans-serif", "typography_font_size": {"unit": "px", "size": 18, "sizes": []}, "margin": {"unit": "px", "top": "0", "right": "0", "bottom": "30", "left": "0", "isLinked": false}}, "elements": []}, {"id": "hero_button", "elType": "widget", "widgetType": "button", "isInner": false, "settings": {"text": "Get Started Today", "align": "center", "button_text_color": "#ffffff", "background_color": "#ff6b6b", "border_radius": {"unit": "px", "top": "25", "right": "25", "bottom": "25", "left": "25", "isLinked": true}, "button_padding": {"unit": "px", "top": "15", "right": "30", "bottom": "15", "left": "30", "isLinked": false}, "typography_typography": "custom", "typography_font_size": {"unit": "px", "size": 16, "sizes": []}, "typography_font_weight": "600", "hover_color": "#ffffff", "button_background_hover_color": "#ff5252"}, "elements": []}]}, {"id": "features001", "elType": "container", "isInner": false, "settings": {"background_background": "classic", "background_color": "#f8f9fa", "padding": {"unit": "px", "top": "80", "right": "20", "bottom": "80", "left": "20", "isLinked": false}}, "elements": [{"id": "features_title", "elType": "widget", "widgetType": "heading", "isInner": false, "settings": {"title": "Our Key Features", "size": "h2", "align": "center", "color": "#333333", "typography_typography": "custom", "typography_font_family": "<PERSON>l, sans-serif", "typography_font_size": {"unit": "px", "size": 36, "sizes": []}, "typography_font_weight": "bold", "margin": {"unit": "px", "top": "0", "right": "0", "bottom": "50", "left": "0", "isLinked": false}}, "elements": []}, {"id": "features_row", "elType": "container", "isInner": false, "settings": {"flex_direction": "row", "flex_wrap": "wrap", "gap": {"unit": "px", "size": 30, "sizes": []}}, "elements": [{"id": "feature1", "elType": "container", "isInner": true, "settings": {"background_background": "classic", "background_color": "#ffffff", "padding": {"unit": "px", "top": "30", "right": "25", "bottom": "30", "left": "25", "isLinked": false}, "border_radius": {"unit": "px", "top": "10", "right": "10", "bottom": "10", "left": "10", "isLinked": true}, "box_shadow_box_shadow_type": "yes", "box_shadow_box_shadow": {"horizontal": 0, "vertical": 5, "blur": 15, "spread": 0, "color": "rgba(0,0,0,0.1)"}, "flex_basis": {"unit": "%", "size": 30, "sizes": []}}, "elements": [{"id": "feature1_icon", "elType": "widget", "widgetType": "icon", "isInner": false, "settings": {"selected_icon": {"value": "fas fa-rocket", "library": "fa-solid"}, "primary_color": "#667eea", "size": {"unit": "px", "size": 48, "sizes": []}, "align": "center", "margin": {"unit": "px", "top": "0", "right": "0", "bottom": "20", "left": "0", "isLinked": false}}, "elements": []}, {"id": "feature1_title", "elType": "widget", "widgetType": "heading", "isInner": false, "settings": {"title": "Fast Performance", "size": "h3", "align": "center", "color": "#333333", "typography_typography": "custom", "typography_font_size": {"unit": "px", "size": 24, "sizes": []}, "typography_font_weight": "600", "margin": {"unit": "px", "top": "0", "right": "0", "bottom": "15", "left": "0", "isLinked": false}}, "elements": []}, {"id": "feature1_desc", "elType": "widget", "widgetType": "text-editor", "isInner": false, "settings": {"editor": "<p>Lightning-fast loading times and optimized performance for the best user experience.</p>", "align": "center", "text_color": "#666666", "typography_typography": "custom", "typography_font_size": {"unit": "px", "size": 16, "sizes": []}}, "elements": []}]}, {"id": "feature2", "elType": "container", "isInner": true, "settings": {"background_background": "classic", "background_color": "#ffffff", "padding": {"unit": "px", "top": "30", "right": "25", "bottom": "30", "left": "25", "isLinked": false}, "border_radius": {"unit": "px", "top": "10", "right": "10", "bottom": "10", "left": "10", "isLinked": true}, "box_shadow_box_shadow_type": "yes", "box_shadow_box_shadow": {"horizontal": 0, "vertical": 5, "blur": 15, "spread": 0, "color": "rgba(0,0,0,0.1)"}, "flex_basis": {"unit": "%", "size": 30, "sizes": []}}, "elements": [{"id": "feature2_icon", "elType": "widget", "widgetType": "icon", "isInner": false, "settings": {"selected_icon": {"value": "fas fa-shield-alt", "library": "fa-solid"}, "primary_color": "#667eea", "size": {"unit": "px", "size": 48, "sizes": []}, "align": "center", "margin": {"unit": "px", "top": "0", "right": "0", "bottom": "20", "left": "0", "isLinked": false}}, "elements": []}, {"id": "feature2_title", "elType": "widget", "widgetType": "heading", "isInner": false, "settings": {"title": "Secure & Reliable", "size": "h3", "align": "center", "color": "#333333", "typography_typography": "custom", "typography_font_size": {"unit": "px", "size": 24, "sizes": []}, "typography_font_weight": "600", "margin": {"unit": "px", "top": "0", "right": "0", "bottom": "15", "left": "0", "isLinked": false}}, "elements": []}, {"id": "feature2_desc", "elType": "widget", "widgetType": "text-editor", "isInner": false, "settings": {"editor": "<p>Enterprise-grade security with 99.9% uptime guarantee for your peace of mind.</p>", "align": "center", "text_color": "#666666", "typography_typography": "custom", "typography_font_size": {"unit": "px", "size": 16, "sizes": []}}, "elements": []}]}, {"id": "feature3", "elType": "container", "isInner": true, "settings": {"background_background": "classic", "background_color": "#ffffff", "padding": {"unit": "px", "top": "30", "right": "25", "bottom": "30", "left": "25", "isLinked": false}, "border_radius": {"unit": "px", "top": "10", "right": "10", "bottom": "10", "left": "10", "isLinked": true}, "box_shadow_box_shadow_type": "yes", "box_shadow_box_shadow": {"horizontal": 0, "vertical": 5, "blur": 15, "spread": 0, "color": "rgba(0,0,0,0.1)"}, "flex_basis": {"unit": "%", "size": 30, "sizes": []}}, "elements": [{"id": "feature3_icon", "elType": "widget", "widgetType": "icon", "isInner": false, "settings": {"selected_icon": {"value": "fas fa-users", "library": "fa-solid"}, "primary_color": "#667eea", "size": {"unit": "px", "size": 48, "sizes": []}, "align": "center", "margin": {"unit": "px", "top": "0", "right": "0", "bottom": "20", "left": "0", "isLinked": false}}, "elements": []}, {"id": "feature3_title", "elType": "widget", "widgetType": "heading", "isInner": false, "settings": {"title": "24/7 Support", "size": "h3", "align": "center", "color": "#333333", "typography_typography": "custom", "typography_font_size": {"unit": "px", "size": 24, "sizes": []}, "typography_font_weight": "600", "margin": {"unit": "px", "top": "0", "right": "0", "bottom": "15", "left": "0", "isLinked": false}}, "elements": []}, {"id": "feature3_desc", "elType": "widget", "widgetType": "text-editor", "isInner": false, "settings": {"editor": "<p>Round-the-clock customer support from our dedicated team of experts.</p>", "align": "center", "text_color": "#666666", "typography_typography": "custom", "typography_font_size": {"unit": "px", "size": 16, "sizes": []}}, "elements": []}]}]}]}, {"id": "about001", "elType": "container", "isInner": false, "settings": {"background_background": "classic", "background_color": "#ffffff", "padding": {"unit": "px", "top": "80", "right": "20", "bottom": "80", "left": "20", "isLinked": false}}, "elements": [{"id": "about_container", "elType": "container", "isInner": false, "settings": {"flex_direction": "row", "gap": {"unit": "px", "size": 50, "sizes": []}, "align_items": "center"}, "elements": [{"id": "about_content", "elType": "container", "isInner": true, "settings": {"flex_basis": {"unit": "%", "size": 50, "sizes": []}}, "elements": [{"id": "about_title", "elType": "widget", "widgetType": "heading", "isInner": false, "settings": {"title": "About Our Company", "size": "h2", "align": "left", "color": "#333333", "typography_typography": "custom", "typography_font_family": "<PERSON>l, sans-serif", "typography_font_size": {"unit": "px", "size": 36, "sizes": []}, "typography_font_weight": "bold", "margin": {"unit": "px", "top": "0", "right": "0", "bottom": "25", "left": "0", "isLinked": false}}, "elements": []}, {"id": "about_text", "elType": "widget", "widgetType": "text-editor", "isInner": false, "settings": {"editor": "<p>We are a leading technology company dedicated to providing innovative solutions that help businesses thrive in the digital age. With over 10 years of experience, we have successfully served thousands of clients worldwide.</p><p>Our team of experts is committed to delivering exceptional results and ensuring customer satisfaction through cutting-edge technology and personalized service.</p>", "align": "left", "text_color": "#666666", "typography_typography": "custom", "typography_font_size": {"unit": "px", "size": 16, "sizes": []}, "typography_line_height": {"unit": "em", "size": 1.6, "sizes": []}, "margin": {"unit": "px", "top": "0", "right": "0", "bottom": "30", "left": "0", "isLinked": false}}, "elements": []}, {"id": "about_button", "elType": "widget", "widgetType": "button", "isInner": false, "settings": {"text": "Learn More", "align": "left", "button_text_color": "#ffffff", "background_color": "#667eea", "border_radius": {"unit": "px", "top": "5", "right": "5", "bottom": "5", "left": "5", "isLinked": true}, "button_padding": {"unit": "px", "top": "12", "right": "25", "bottom": "12", "left": "25", "isLinked": false}, "typography_typography": "custom", "typography_font_size": {"unit": "px", "size": 16, "sizes": []}, "typography_font_weight": "600", "hover_color": "#ffffff", "button_background_hover_color": "#5a67d8"}, "elements": []}]}, {"id": "about_image", "elType": "widget", "widgetType": "image", "isInner": false, "settings": {"image": {"url": "https://via.placeholder.com/500x400/667eea/ffffff?text=About+Us", "id": ""}, "image_size": "full", "align": "center", "border_radius": {"unit": "px", "top": "10", "right": "10", "bottom": "10", "left": "10", "isLinked": true}}, "elements": []}]}]}, {"id": "contact001", "elType": "container", "isInner": false, "settings": {"background_background": "classic", "background_color": "#f8f9fa", "padding": {"unit": "px", "top": "80", "right": "20", "bottom": "80", "left": "20", "isLinked": false}}, "elements": [{"id": "contact_title", "elType": "widget", "widgetType": "heading", "isInner": false, "settings": {"title": "Get In Touch", "size": "h2", "align": "center", "color": "#333333", "typography_typography": "custom", "typography_font_family": "<PERSON>l, sans-serif", "typography_font_size": {"unit": "px", "size": 36, "sizes": []}, "typography_font_weight": "bold", "margin": {"unit": "px", "top": "0", "right": "0", "bottom": "20", "left": "0", "isLinked": false}}, "elements": []}, {"id": "contact_subtitle", "elType": "widget", "widgetType": "text-editor", "isInner": false, "settings": {"editor": "<p>Ready to get started? Contact us today and let's discuss how we can help your business grow.</p>", "align": "center", "text_color": "#666666", "typography_typography": "custom", "typography_font_size": {"unit": "px", "size": 18, "sizes": []}, "margin": {"unit": "px", "top": "0", "right": "0", "bottom": "50", "left": "0", "isLinked": false}}, "elements": []}, {"id": "contact_info", "elType": "container", "isInner": false, "settings": {"flex_direction": "row", "gap": {"unit": "px", "size": 40, "sizes": []}, "justify_content": "center", "flex_wrap": "wrap"}, "elements": [{"id": "contact_email", "elType": "container", "isInner": true, "settings": {"background_background": "classic", "background_color": "#ffffff", "padding": {"unit": "px", "top": "30", "right": "25", "bottom": "30", "left": "25", "isLinked": false}, "border_radius": {"unit": "px", "top": "10", "right": "10", "bottom": "10", "left": "10", "isLinked": true}, "box_shadow_box_shadow_type": "yes", "box_shadow_box_shadow": {"horizontal": 0, "vertical": 5, "blur": 15, "spread": 0, "color": "rgba(0,0,0,0.1)"}, "text_align": "center"}, "elements": [{"id": "email_icon", "elType": "widget", "widgetType": "icon", "isInner": false, "settings": {"selected_icon": {"value": "fas fa-envelope", "library": "fa-solid"}, "primary_color": "#667eea", "size": {"unit": "px", "size": 32, "sizes": []}, "align": "center", "margin": {"unit": "px", "top": "0", "right": "0", "bottom": "15", "left": "0", "isLinked": false}}, "elements": []}, {"id": "email_text", "elType": "widget", "widgetType": "text-editor", "isInner": false, "settings": {"editor": "<p><strong>Email Us</strong><br><EMAIL></p>", "align": "center", "text_color": "#333333", "typography_typography": "custom", "typography_font_size": {"unit": "px", "size": 16, "sizes": []}}, "elements": []}]}, {"id": "contact_phone", "elType": "container", "isInner": true, "settings": {"background_background": "classic", "background_color": "#ffffff", "padding": {"unit": "px", "top": "30", "right": "25", "bottom": "30", "left": "25", "isLinked": false}, "border_radius": {"unit": "px", "top": "10", "right": "10", "bottom": "10", "left": "10", "isLinked": true}, "box_shadow_box_shadow_type": "yes", "box_shadow_box_shadow": {"horizontal": 0, "vertical": 5, "blur": 15, "spread": 0, "color": "rgba(0,0,0,0.1)"}, "text_align": "center"}, "elements": [{"id": "phone_icon", "elType": "widget", "widgetType": "icon", "isInner": false, "settings": {"selected_icon": {"value": "fas fa-phone", "library": "fa-solid"}, "primary_color": "#667eea", "size": {"unit": "px", "size": 32, "sizes": []}, "align": "center", "margin": {"unit": "px", "top": "0", "right": "0", "bottom": "15", "left": "0", "isLinked": false}}, "elements": []}, {"id": "phone_text", "elType": "widget", "widgetType": "text-editor", "isInner": false, "settings": {"editor": "<p><strong>Call Us</strong><br>+****************</p>", "align": "center", "text_color": "#333333", "typography_typography": "custom", "typography_font_size": {"unit": "px", "size": 16, "sizes": []}}, "elements": []}]}]}]}]}